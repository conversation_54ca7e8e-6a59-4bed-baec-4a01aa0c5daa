from datetime import datetime, timezone
from enum import Enum

from sqlalchemy import <PERSON>SO<PERSON>, Column, DateTime, Float, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship
from src.models.custom_types import UuidVariant
from src.models.database import Base


class ProjectStatus(str, Enum):
    """Project status enumeration for tracking project lifecycle."""

    UNINITIALIZED = "uninitialized"
    ACTIVE = "active"
    SCANNING = "scanning"
    INDEXING = "indexing"
    COMPLETED = "completed"
    FAILED = "failed"


class Project(Base):
    """Project model with UUID-based ownership system."""

    __tablename__ = "projects"

    # Primary key
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # UUID-based ownership - direct foreign key to user's supabase_user_id
    owner_id = Column(
        UuidVariant,
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="UUID foreign key to user_profiles.supabase_user_id",
    )

    # Project metadata
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    repository_url = Column(String(500), nullable=True)

    # Multi-tenant database schema fields
    db_schema_name = Column(String(255), nullable=True, unique=True)
    db_connection_url = Column(String(500), nullable=True)

    # Status tracking fields
    status = Column(
        String(50), nullable=False, default=ProjectStatus.UNINITIALIZED.value, index=True
    )
    progress_percentage = Column(Float, default=0.0)
    last_activity_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))

    # Scanning and indexing tracking
    scan_started_at = Column(DateTime(timezone=True), nullable=True)
    scan_completed_at = Column(DateTime(timezone=True), nullable=True)
    total_files_scanned = Column(Integer, default=0)
    total_files_indexed = Column(Integer, default=0)
    scan_error_message = Column(Text, nullable=True)

    # Docker resource names for sandboxed projects
    docker_volume_name = Column(String(255), nullable=True, unique=True)
    docker_network_name = Column(String(255), nullable=True, unique=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Owner relationship
    owner = relationship("UserProfile", back_populates="owned_projects", lazy="selectin")

    # Members relationship
    members = relationship(
        "UserProfile",
        secondary="user_project_association",
        back_populates="projects",
        lazy="selectin",
    )

    # Note: completion_percentage calculation moved to ProjectService.get_project_completion_percentage()
    # for async database operations. Use that method instead of this property.

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"


class ProjectExport(Base):
    """Project export model for tracking export operations."""

    __tablename__ = "project_exports"

    id = Column(Integer, primary_key=True)
    export_id = Column(String(255), unique=True, nullable=False, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=False)

    # Export metadata
    format = Column(String(50), nullable=False)  # 'zip', 'tar.gz', etc.
    status = Column(
        String(50), nullable=False, default="pending"
    )  # 'pending', 'processing', 'completed', 'failed'
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)  # Size in bytes

    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    current_step = Column(String(255), nullable=True)
    total_steps = Column(Integer, default=1)

    # Error handling
    error_message = Column(Text, nullable=True)

    # Metadata
    export_metadata = Column(JSON, nullable=True)  # Store additional export info as JSON
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    project = relationship("Project", backref="exports", lazy="selectin")
    user = relationship("UserProfile", backref="exports", lazy="selectin")

    def __repr__(self):
        return f"<ProjectExport(id={self.id}, export_id={self.export_id}, status={self.status})>"
