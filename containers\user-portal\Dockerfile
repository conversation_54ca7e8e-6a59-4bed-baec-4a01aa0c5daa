# syntax=docker/dockerfile:1
# Multi-stage Dockerfile for Next.js application with standalone output
# ============================================================================
# DEPENDENCIES STAGE: Install all dependencies
# ============================================================================
FROM node:20-slim AS deps

# Set working directory
WORKDIR /app

# Layer 1: Copy package files only (for optimal caching)
COPY package*.json .npmrc* ./

# Layer 2: Install dependencies with cache mount
RUN --mount=type=cache,target=/root/.npm \
  npm ci --legacy-peer-deps

# ============================================================================
# BUILDER STAGE: Build the application
# ============================================================================
FROM node:20-slim AS builder

# Set working directory
WORKDIR /app

# Layer 1: Copy dependencies from deps stage
COPY --link --from=deps /app/node_modules ./node_modules

# Layer 2: Copy package files (for scripts access)
COPY package*.json ./

# Layer 3: Copy configuration files (changes less frequently)
COPY next.config.* tsconfig.json tailwind.config.* postcss.config.* ./
COPY .env.production* ./

# Layer 4: Copy public assets (changes occasionally)
COPY public ./public

# Layer 5: Copy source code (changes most frequently)
COPY src ./src

# Layer 6: Build the application with standalone output
RUN npm run build

# ============================================================================
# PRODUCTION STAGE: Minimal runtime image
# ============================================================================
FROM node:20-slim AS production

# Layer 1: Install runtime dependencies with cache mounts
RUN --mount=type=cache,target=/var/cache/apt \
  --mount=type=cache,target=/var/lib/apt \
  apt-get update && \
  apt-get install -y --no-install-recommends \
  curl \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Layer 2: Create non-root user (rarely changes)
RUN groupadd --system --gid 1001 nodejs && \
  useradd --system --uid 1001 --gid nodejs --shell /bin/bash --create-home nextjs

# Set working directory
WORKDIR /app

# Layer 3: Copy public directory (static assets)
COPY --link --from=builder --chown=nextjs:nodejs /app/public ./public

# Layer 4: Copy standalone application
COPY --link --from=builder --chown=nextjs:nodejs /app/.next/standalone ./

# Layer 5: Copy static assets generated during build
COPY --link --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to non-root user
USER nextjs

# Expose application port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Labels
LABEL org.opencontainers.image.title="AI Coding Agent - User Portal" \
  org.opencontainers.image.description="Next.js-based user portal" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  security.non-root="true" \
  security.user="nextjs"

# Start the Next.js standalone server
CMD ["node", "server.js"]