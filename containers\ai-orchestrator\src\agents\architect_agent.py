"""
Sequential Agents - Unified ArchitectAgent

This agent is responsible for two primary functions:
1.  **Interactive Interviewing**: Conducts structured interviews with users to gather project requirements and generates comprehensive roadmaps.
2.  **Planning and Delegation**: Performs multi-step planning using an LLM and delegates work by creating specialist tasks in the database.
"""

from __future__ import annotations

import json
import logging
import re
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy import select
from src.agents.base_agent import BaseAgent, ValidationResult
from src.models import InterviewState
from src.models.project import Project, ProjectStatus
from src.repository.project_repository import ProjectRepository
from src.services.enhanced_llm_service import EnhancedLLMService
from src.services.rag_service import RAGService

# Import other dependencies inside methods to avoid circular imports

logger = logging.getLogger(__name__)


class InterviewQuestion:
    """Represents a question in the interview flow."""

    def __init__(
        self,
        key: str,
        question: str,
        sequence_order: int,
        is_followup: bool = False,
        validation_rules: Optional[Dict[str, Any]] = None,
    ):
        self.key = key
        self.question = question
        self.sequence_order = sequence_order
        self.is_followup = is_followup
        self.validation_rules = validation_rules or {}


class ArchitectAgent(BaseAgent):
    """
    Unified Architect that handles both user interviews and project planning.
    """

    def __init__(
        self,
        memory_service: Optional[Any] = None,
        rag_service: Optional[RAGService] = None,
        llm_service: Optional[EnhancedLLMService] = None,
        project_repository: Optional[ProjectRepository] = None,
    ) -> None:
        super().__init__()
        self._llm = llm_service
        self.rag_service = rag_service
        self.project_repository = project_repository
        # For interviews
        self._conversation_repo = None
        self._base_questions = self._get_base_questions()
        self._max_followup_questions = 3
        # Memory integration
        self.memory_service = memory_service

    async def handle_user_prompt(self, owner_id: uuid.UUID, project_id: str, prompt: str) -> dict:
        """
        Primary entry point for user prompts. Differentiates between new projects
        and modification requests based on the project's state.
        """
        imports = self._get_imports()
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            if not self.project_repository:
                # In a real app, this would be injected. For now, we'll instantiate it.
                from src.services.redis_service import get_project_cache

                project_cache = await get_project_cache()
                self.project_repository = ProjectRepository(project_cache)

            project = await self.project_repository.get_project_by_id(
                user_id=str(owner_id), project_id=project_id, db=db
            )

            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Project with ID {project_id} not found or access denied.",
                )

            if project.status == ProjectStatus.UNINITIALIZED.value:
                # New project flow
                return await self._handle_new_project_intake(
                    owner_id=owner_id, project_id=project_id, prompt=prompt
                )
            else:
                # Existing project modification flow
                return await self._handle_modification_request(
                    owner_id=owner_id, project_id=project_id, prompt=prompt
                )

    async def _handle_new_project_intake(
        self, owner_id: uuid.UUID, project_id: str, prompt: str
    ) -> dict:
        """Handles the intake process for a new project, starting an interview."""
        # For now, we'll just log and start the interview.
        # This can be expanded to use the prompt to pre-fill some interview answers.
        logger.info(
            f"Starting new project intake for project {project_id} based on prompt: '{prompt}'"
        )
        session_id = await self.start_interview(project_id=project_id, user_id=str(owner_id))
        return {
            "status": "interview_started",
            "session_id": session_id,
            "message": "New project detected. Starting the interview process.",
        }

    async def _handle_modification_request(
        self, owner_id: uuid.UUID, project_id: str, prompt: str
    ) -> dict:
        """
        Handles a request to modify an existing project by generating a delta roadmap.
        """
        logger.info(f"Handling modification request for project {project_id}: '{prompt}'")
        imports = self._get_imports()
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            # 1. Full Codebase Context (RAG)
            if not self.rag_service:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="RAGService is not available.",
                )
            context_result = await self.rag_service.get_context(
                query=prompt, user_id=str(owner_id), project_id=project_id
            )
            rag_context = [doc.content for doc in context_result.documents]

            # 2. Prompt Construction for Delta Roadmap
            delta_roadmap_prompt = f"""
            You are an expert software engineer. Based on the user's request and the provided code context,
            generate a short, step-by-step plan (a delta roadmap) of FrontendAgent and ShellAgent tasks
            required to implement this change.

            User's Request: "{prompt}"

            Relevant Code Context:
            ---
            {json.dumps(rag_context, indent=2)}
            ---

            Instructions:
            - The first step MUST be a 'shell' task to create a new git branch.
            - For each step, specify the 'agent_role' ('frontend', 'backend', or 'shell') and a 'task_description'.
            - Keep descriptions concise and actionable.
            - Return ONLY a valid JSON object in the following format:
            {{
                "branch_name": "feature/short-description-of-change",
                "plan": [
                    {{
                        "agent_role": "shell",
                        "task_description": "Create a new git branch named 'feature/short-description-of-change'"
                    }},
                    {{
                        "agent_role": "frontend",
                        "task_description": "Update the header component in 'src/components/Header.js' to have a blue background."
                    }}
                ]
            }}
            """

            # 3. LLM Call
            llm = await self._get_llm()
            GenerateRequest = imports["GenerateRequest"]
            req = GenerateRequest(prompt=delta_roadmap_prompt)
            resp = await llm.generate(req, user_id=str(owner_id))

            # 4. Parse and Dispatch Tasks
            try:
                delta_roadmap = self._extract_json(resp.content)
                if not delta_roadmap.get("plan"):
                    raise ValueError("LLM response did not contain a 'plan'.")
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Failed to parse delta roadmap from LLM: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to generate a valid modification plan: {e}",
                )

            TaskRepository = imports["TaskRepository"]
            created_task_ids = []
            plan_steps = delta_roadmap.get("plan", [])

            project = await self.project_repository.get_project_by_id(
                user_id=str(owner_id), project_id=project_id, db=db
            )

            for step in plan_steps:
                input_data = {
                    "feature": step["task_description"],
                    "source": "architect_delta_roadmap",
                    "goal": prompt,
                    "project_id": project_id,
                    "db_connection_url": project.db_connection_url if project else None,
                    "db_schema_name": project.db_schema_name if project else None,
                }
                # Specific handling for shell commands like git branching
                if step["agent_role"] == "shell":
                    input_data["commands"] = [step["task_description"]]

                task = await TaskRepository.create_task(
                    db,
                    project_id=project_id,
                    agent_role=step["agent_role"],
                    input_data=input_data,
                )
                created_task_ids.append(task.id)

            logger.info(
                f"Dispatched {len(created_task_ids)} tasks for modification request on project {project_id}."
            )

            return {
                "status": "modification_plan_generated",
                "project_id": project_id,
                "branch_name": delta_roadmap.get("branch_name", "unknown-branch"),
                "plan": delta_roadmap.get("plan", []),
                "created_task_ids": created_task_ids,
            }

        except Exception as e:
            logger.error(
                f"Error handling modification request for project {project_id}: {e}", exc_info=True
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred while handling the modification request: {e}",
            )

    async def improve_roadmap(self, owner_id: uuid.UUID, existing_roadmap: dict) -> dict:
        """
        Improves an existing roadmap using RAG and LLM.
        Args:
            owner_id: The UUID of the owner.
            existing_roadmap: The existing roadmap dictionary.
        Returns:
            A dictionary containing the improved roadmap and source references.
        """
        if not self.rag_service or not self._llm:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="RAGService or EnhancedLLMService is not available.",
            )

        all_context = []
        collected_source_references = []

        # 1. Context Retrieval (RAG)
        if "phases" in existing_roadmap:
            for phase in existing_roadmap["phases"]:
                description = phase.get("description", "")
                if description:
                    # Correctly call get_context which is available in RAGService
                    context_result = await self.rag_service.get_context(
                        query=description, user_id=str(owner_id)
                    )
                    for doc in context_result.documents:
                        all_context.append(doc.content)
                        # Assuming doc.metadata contains source info
                        if "source" in doc.metadata:
                            collected_source_references.append(doc.metadata["source"])

        # 2. Prompt Construction
        prompt = f"""
        You are an expert project manager. Your task is to enhance the following roadmap.
        Please add more detailed tasks, include acceptance criteria, identify potential risks,
        and ensure all necessary steps for a production-ready application are included.
        Existing Roadmap:
        {json.dumps(existing_roadmap, indent=2)}
        Relevant Context from Knowledge Base:
        {json.dumps(all_context, indent=2)}
        Instructions:
        - Enhance the roadmap with detailed tasks.
        - Add acceptance criteria for each major feature or phase.
        - Identify potential risks and suggest mitigations.
        - Ensure the output is a valid JSON object with a "phases" key.
        Return ONLY the improved roadmap as a JSON object.
        """

        # 3. LLM Call
        from src.models.llm_models import GenerateRequest

        req = GenerateRequest(prompt=prompt)
        resp = await self._llm.generate(req, user_id=str(owner_id))

        # 4. Parse and Validate
        try:
            # Attempt to extract JSON from the response, even if it's wrapped in markdown
            match = re.search(r"```(?:json)?\n(.*?)```", resp.content, re.DOTALL)
            if match:
                json_text = match.group(1)
            else:
                json_text = resp.content

            validated_llm_json = json.loads(json_text.strip())

            if "phases" not in validated_llm_json:
                raise ValueError("Invalid JSON structure: 'phases' key is missing.")
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse or validate LLM response: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get a valid roadmap from the AI.",
            ) from e

        # 5. Return Structured Output
        return {
            "improved_roadmap": validated_llm_json,
            "source_refs": list(set(collected_source_references)),
        }

    def _get_imports(self):
        """Get imports inside method to avoid circular imports."""
        try:
            from src.models import ConversationHistory, InterviewSession, InterviewState
            from src.models.database_async import get_async_db
            from src.models.llm_models import GenerateRequest, LLMResponse
            from src.repository.conversation_repository import ConversationRepository
            from src.repository.task_repository import TaskRepository
            from src.services.enhanced_llm_service import get_llm_service
            from src.services.memory_management_service import MemoryManagementService, MemoryType
        except ImportError:
            from src.models import ConversationHistory, InterviewSession, InterviewState
            from src.models.database_async import get_async_db
            from src.models.llm_models import GenerateRequest, LLMResponse
            from src.repository.conversation_repository import ConversationRepository
            from src.repository.task_repository import TaskRepository
            from src.router.llm_router import get_llm_service
            from src.services.memory_management_service import MemoryManagementService, MemoryType

        return {
            "get_llm_service": get_llm_service,
            "GenerateRequest": GenerateRequest,
            "LLMResponse": LLMResponse,
            "TaskRepository": TaskRepository,
            "ConversationRepository": ConversationRepository,
            "InterviewSession": InterviewSession,
            "InterviewState": InterviewState,
            "ConversationHistory": ConversationHistory,
            "get_async_db": get_async_db,
            "MemoryManagementService": MemoryManagementService,
            "MemoryType": MemoryType,
        }

    async def _get_llm(self):
        if self._llm is None:
            imports = self._get_imports()
            self._llm = await imports["get_llm_service"]()
        return self._llm

    # =====================================================================================
    # Main Executor
    # =====================================================================================

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the appropriate flow based on task_input.
        - If 'session_id' or 'start_interview' is present, it's an interview task.
        - Otherwise, it's a planning/delegation task.
        """
        if "session_id" in task_input or task_input.get("start_interview"):
            return await self._execute_interview_flow(task_input)
        else:
            return await self._execute_planning_flow(task_input)

    # =====================================================================================
    # Interview Flow Methods
    # =====================================================================================

    def _get_base_questions(self) -> List[InterviewQuestion]:
        """Get the base set of interview questions."""
        return [
            InterviewQuestion(
                key="project_name",
                question="What is the name of your project?",
                sequence_order=1,
                validation_rules={"required": True, "min_length": 2},
            ),
            InterviewQuestion(
                key="project_type",
                question="What type of project are you building? (e.g., web app, mobile app, API, etc.)",
                sequence_order=2,
                validation_rules={"required": True},
            ),
            InterviewQuestion(
                key="target_audience",
                question="Who is your target audience?",
                sequence_order=3,
                validation_rules={"required": True},
            ),
            InterviewQuestion(
                key="key_features",
                question="What are the key features you want to implement?",
                sequence_order=4,
                validation_rules={"required": True},
            ),
            InterviewQuestion(
                key="technology_stack",
                question="Do you have any preferred technology stack or frameworks?",
                sequence_order=5,
                validation_rules={"required": False},
            ),
            InterviewQuestion(
                key="timeline",
                question="What is your expected timeline for this project?",
                sequence_order=6,
                validation_rules={"required": False},
            ),
            InterviewQuestion(
                key="budget",
                question="What is your budget range for this project?",
                sequence_order=7,
                validation_rules={"required": False},
            ),
            InterviewQuestion(
                key="existing_code",
                question="Do you have any existing code or systems to integrate with?",
                sequence_order=8,
                validation_rules={"required": False},
            ),
        ]

    async def _get_conversation_repo(self):
        if self._conversation_repo is None:
            imports = self._get_imports()
            self._conversation_repo = imports["ConversationRepository"]
        return self._conversation_repo

    async def _execute_interview_flow(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the interactive interview state machine.
        """
        project_id = task_input.get("project_id")
        user_id = task_input.get("user_id")
        session_id = task_input.get("session_id")
        user_response = task_input.get("user_response")

        if not all([project_id, user_id]) and not session_id:
            return {
                "agent": "architect",
                "status": "invalid_input",
                "error": "'project_id' and 'user_id' are required for new interviews, or 'session_id' for existing ones.",
            }

        imports = self._get_imports()
        get_async_db = imports["get_async_db"]
        ConversationRepository = await self._get_conversation_repo()

        async with get_async_db() as db:
            if not session_id:
                session_id = await self._create_new_session(
                    db, project_id, user_id, ConversationRepository
                )
                return {
                    "agent": "architect",
                    "status": "session_created",
                    "session_id": session_id,
                    "message": "Welcome! Let's start your project interview.",
                    "next_action": "greeting",
                }

            session = await ConversationRepository.get_interview_session(db, session_id)
            if not session:
                return {
                    "agent": "architect",
                    "status": "error",
                    "error": f"Session {session_id} not found",
                }

            if user_response is not None:
                await self._process_user_response_interview(
                    db, session_id, user_response, ConversationRepository
                )

            return await self._execute_state_machine(db, session, ConversationRepository)

    async def start_interview(self, project_id: int, user_id: str) -> str:
        """
        Start a new interview session.
        """
        task_input = {
            "project_id": project_id,
            "user_id": user_id,
            "start_interview": True,
        }
        result = await self.execute(task_input)
        return result.get("session_id", "")

    async def get_current_question(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current question for a session.
        """
        task_input = {"session_id": session_id}
        result = await self.execute(task_input)
        if result.get("status") == "asking_question":
            return {
                "type": "question",
                "message": result.get("message"),
                "question_key": result.get("question", {}).get("key"),
                "question_text": result.get("question", {}).get("text"),
                "is_followup": result.get("question", {}).get("is_followup"),
                "session_id": session_id,
            }
        elif result.get("status") == "roadmap_generated":
            return {
                "type": "roadmap",
                "message": result.get("message"),
                "roadmap": result.get("roadmap"),
                "session_id": session_id,
            }
        return None

    async def process_user_response(
        self, session_id: str, question_key: str, user_response: str
    ) -> Dict[str, Any]:
        """
        Process a user response to a question.
        """
        task_input = {"session_id": session_id, "user_response": user_response}
        result = await self.execute(task_input)
        return {
            "status": result.get("status"),
            "message": result.get("message"),
            "next_action": result.get("next_action"),
        }

    async def _create_new_session(
        self, db, project_id: int, user_id: str, ConversationRepository
    ) -> str:
        """Create a new interview session."""
        session_id = str(uuid.uuid4())

        for question in self._base_questions:
            await ConversationRepository.save_response(
                db=db,
                project_id=project_id,
                user_id=user_id,
                session_id=session_id,
                question_key=question.key,
                question_text=question.question,
                sequence_order=question.sequence_order,
                is_followup=question.is_followup,
            )

        await ConversationRepository.create_interview_session(
            db=db,
            project_id=project_id,
            user_id=user_id,
            session_id=session_id,
            total_questions=len(self._base_questions),
        )

        return session_id

    async def _process_user_response_interview(
        self, db, session_id: str, user_response: str, ConversationRepository
    ):
        """Process and save user's response to the current question."""
        current_question = await ConversationRepository.get_next_unanswered_question(db, session_id)

        if current_question:
            await ConversationRepository.update_response(
                db=db, conversation_id=current_question.id, user_response=user_response
            )
            await self._generate_followup_questions(
                db, current_question, user_response, ConversationRepository
            )

    async def _generate_followup_questions(
        self, db, current_question, user_response: str, ConversationRepository
    ):
        """Generate follow-up questions based on user's response using LLM."""
        if current_question.is_followup or current_question.sequence_order > len(
            self._base_questions
        ):
            return

        followup_prompt = f"""
        Based on the user's response to: "{current_question.question_text}"
        User said: "{user_response}"

        Should we ask follow-up questions to clarify or expand on this? If yes, generate 1-2 specific follow-up questions.
        Return ONLY JSON in this format:
        {{
            "needs_followup": true/false,
            "followup_questions": [
                {{
                    "key": "followup_1",
                    "question": "Specific follow-up question here?"
                }}
            ]
        }}
        """

        try:
            llm = await self._get_llm()
            imports = self._get_imports()
            GenerateRequest = imports["GenerateRequest"]

            req = GenerateRequest(prompt=followup_prompt)
            resp = await llm.generate(req, user_id="architect-agent")

            followup_data = json.loads(resp.content.strip())
            if followup_data.get("needs_followup", False):
                for i, fq in enumerate(followup_data.get("followup_questions", [])):
                    await ConversationRepository.save_response(
                        db=db,
                        project_id=current_question.project_id,
                        user_id=current_question.user_id,
                        session_id=current_question.session_id,
                        question_key=f"followup_{current_question.sequence_order}_{i + 1}",
                        question_text=fq["question"],
                        sequence_order=current_question.sequence_order + (i + 1) * 0.1,
                        is_followup=True,
                    )
        except Exception as e:
            logger.warning(f"Failed to generate follow-up questions: {e}")

    async def _execute_state_machine(self, db, session, ConversationRepository) -> Dict[str, Any]:
        """Execute the interview state machine based on current session state."""
        current_state = InterviewState(session.current_state)

        if current_state == InterviewState.GREETING:
            return await self._handle_greeting_state(db, session, ConversationRepository)
        elif current_state == InterviewState.ASKING_QUESTIONS:
            return await self._handle_asking_questions_state(db, session, ConversationRepository)
        elif current_state == InterviewState.GENERATING_ROADMAP:
            return await self._handle_generating_roadmap_state(db, session, ConversationRepository)
        elif current_state == InterviewState.COMPLETE:
            return await self._handle_complete_state(db, session, ConversationRepository)
        else:
            return {
                "agent": "architect",
                "status": "error",
                "error": f"Unknown state: {current_state}",
            }

    async def _handle_greeting_state(self, db, session, ConversationRepository) -> Dict[str, Any]:
        """Handle the greeting state - welcome user and transition to asking questions."""
        await ConversationRepository.update_interview_session_state(
            db=db, session_id=session.session_id, new_state=InterviewState.ASKING_QUESTIONS
        )
        return {
            "agent": "architect",
            "status": "greeting",
            "session_id": session.session_id,
            "message": "Welcome to your project planning interview! I'll ask you a series of questions to understand your project requirements.",
            "next_action": "ask_next_question",
        }

    async def _handle_asking_questions_state(
        self, db, session, ConversationRepository
    ) -> Dict[str, Any]:
        """Handle the asking questions state - present next question or transition to roadmap generation."""
        next_question = await ConversationRepository.get_next_unanswered_question(
            db, session.session_id
        )

        if next_question:
            # Check memory for user preferences to personalize the interview
            user_preferences = ""
            if self.memory_service:
                try:
                    memory_results = await self.memory_service.search_memories(
                        owner_id=session.user_id,
                        query="user preferences project requirements technology choices",
                        limit=5,
                    )
                    if memory_results:
                        preferences = [
                            mem.content for mem in memory_results if hasattr(mem, "content")
                        ]
                        if preferences:
                            user_preferences = (
                                "\n\nBased on previous interactions, here are some user preferences to consider:\n"
                                + "\n".join(f"- {pref}" for pref in preferences[:3])
                            )
                except Exception as e:
                    logger.warning(f"Failed to retrieve user preferences from memory: {e}")

            return {
                "agent": "architect",
                "status": "asking_question",
                "session_id": session.session_id,
                "question": {
                    "key": next_question.question_key,
                    "text": next_question.question_text,
                    "sequence_order": next_question.sequence_order,
                    "is_followup": next_question.is_followup,
                },
                "user_preferences": user_preferences,
                "next_action": "wait_for_response",
            }
        else:
            await ConversationRepository.update_interview_session_state(
                db=db, session_id=session.session_id, new_state=InterviewState.GENERATING_ROADMAP
            )
            return {
                "agent": "architect",
                "status": "questions_complete",
                "session_id": session.session_id,
                "message": "Great! I've collected all the information I need. Now generating your project roadmap...",
                "next_action": "generate_roadmap",
            }

    async def _handle_generating_roadmap_state(
        self, db, session, ConversationRepository
    ) -> Dict[str, Any]:
        """Handle the roadmap generation state - create detailed project roadmap."""
        try:
            responses = await self._get_responses_for_roadmap(
                db, session.session_id, ConversationRepository
            )

            # --- Start: New Template Application Logic ---

            # 1. Determine the best template from interview responses
            template_prompt = f"""
            Based on the following project requirements, choose the best starter template.

            Available templates:
            - "fastapi-starter": A Python backend using the FastAPI framework. Ideal for API development, data processing, or backend services.
            - "react-vite-tailwind": A modern frontend project using React, Vite, and Tailwind CSS. Ideal for user interfaces, web applications, and dashboards.
            - "blank": A completely empty project. Choose this if the project description is unclear or doesn't fit the other templates.

            User's project information:
            {json.dumps(responses, indent=2)}

            Analyze the user's responses, particularly "project_type" and "key_features".
            Return ONLY the name of the chosen template as a single JSON string (e.g., "react-vite-tailwind").
            """
            chosen_template = "blank"  # Default value
            try:
                llm = await self._get_llm()
                imports = self._get_imports()
                GenerateRequest = imports["GenerateRequest"]
                req = GenerateRequest(
                    prompt=template_prompt, model="gpt-4-turbo"
                )  # Use a capable model for this decision
                resp = await llm.generate(req, user_id="architect-agent-template-selector")

                # Extract JSON string from response
                match = re.search(r'"(.*?)"', resp.content)
                if match:
                    parsed_template = match.group(1).strip()
                    if parsed_template in ["fastapi-starter", "react-vite-tailwind", "blank"]:
                        chosen_template = parsed_template
                        logger.info(
                            f"LLM chose template: '{chosen_template}' for project {session.project_id}"
                        )

                        # Log template selection metrics for monitoring
                        logger.info(
                            "Template selection successful",
                            extra={
                                "template_selected": chosen_template,
                                "project_id": session.project_id,
                                "user_id": session.user_id,
                                "llm_model": "gpt-4-turbo",
                                "selection_method": "llm_analysis",
                                "project_type": responses.get("project_type", "unknown"),
                                "has_key_features": bool(responses.get("key_features")),
                                "response_length": len(resp.content),
                                "parsing_success": True,
                            },
                        )
                    else:
                        logger.warning(
                            f"LLM chose an invalid template: '{parsed_template}'. Defaulting to 'blank'."
                        )

                        # Log invalid template selection for monitoring
                        logger.warning(
                            "Template selection produced invalid result",
                            extra={
                                "invalid_template": parsed_template,
                                "fallback_template": "blank",
                                "project_id": session.project_id,
                                "user_id": session.user_id,
                                "llm_response": resp.content[:200],  # Truncate for logging
                                "parsing_success": False,
                                "error_type": "invalid_template_choice",
                            },
                        )
                else:
                    logger.warning(
                        f"Could not parse template from LLM response: '{resp.content}'. Defaulting to 'blank'."
                    )

                    # Log parsing failure for monitoring
                    logger.warning(
                        "Template selection parsing failed",
                        extra={
                            "fallback_template": "blank",
                            "project_id": session.project_id,
                            "user_id": session.user_id,
                            "llm_response": resp.content[:200],  # Truncate for logging
                            "parsing_success": False,
                            "error_type": "parsing_failure",
                        },
                    )

            except Exception as e:
                logger.error(
                    f"Failed to determine project template via LLM: {e}. Defaulting to 'blank'."
                )

                # Log LLM failure for monitoring
                logger.error(
                    "Template selection LLM call failed",
                    extra={
                        "fallback_template": "blank",
                        "project_id": session.project_id,
                        "user_id": session.user_id,
                        "error_message": str(e),
                        "error_type": type(e).__name__,
                        "llm_model": "gpt-4-turbo",
                        "selection_method": "llm_analysis",
                        "parsing_success": False,
                    },
                )

            # 2. Create a task for the ShellAgent to apply the template
            if chosen_template and chosen_template != "blank":
                try:
                    TaskRepository = imports["TaskRepository"]
                    # Project files are mounted at /app/workspace/{project_name}
                    # We need to get the project name from the database.
                    from src.models.project import Project

                    project = db.query(Project).filter(Project.id == session.project_id).first()

                    if project:
                        project_path = f"/app/workspace/{project.name}"
                        template_path = f"/app/templates/project-templates/{chosen_template}"

                        # Ensure the project directory exists
                        init_command = f"mkdir -p {project_path}"
                        copy_command = f"cp -r {template_path}/. {project_path}/"

                        input_data = {
                            "commands": [init_command, copy_command],
                            "working_directory": "/",
                        }

                        await TaskRepository.create_task(
                            db,
                            project_id=session.project_id,
                            agent_role="shell",
                            input_data=input_data,
                        )
                        logger.info(
                            f"Created ShellAgent task to apply template '{chosen_template}' for project {session.project_id} in path {project_path}"
                        )

                        # Log successful template application task creation
                        logger.info(
                            "Template application task created successfully",
                            extra={
                                "template_applied": chosen_template,
                                "project_id": session.project_id,
                                "user_id": session.user_id,
                                "project_path": project_path,
                                "template_path": template_path,
                                "task_created": True,
                                "agent_type": "shell",
                            },
                        )
                    else:
                        logger.error(
                            f"Could not find project with ID {session.project_id} to apply template."
                        )

                        # Log project not found error
                        logger.error(
                            "Project not found for template application",
                            extra={
                                "template_intended": chosen_template,
                                "project_id": session.project_id,
                                "user_id": session.user_id,
                                "error_type": "project_not_found",
                                "task_created": False,
                            },
                        )

                except Exception as e:
                    logger.error(f"Failed to create ShellAgent task for template application: {e}")

                    # Log template application failure
                    logger.error(
                        "Template application task creation failed",
                        extra={
                            "template_intended": chosen_template,
                            "project_id": session.project_id,
                            "user_id": session.user_id,
                            "error_message": str(e),
                            "error_type": type(e).__name__,
                            "task_created": False,
                            "agent_type": "shell",
                        },
                    )

            # --- End: New Template Application Logic ---

            roadmap = await self._generate_roadmap(responses)
            roadmap_id = str(uuid.uuid4())
            roadmap_data = {
                "id": roadmap_id,
                "project_id": session.project_id,
                "title": f"Roadmap for {responses.get('project_name', 'Project')}",
                "content": roadmap,
                "owner_id": session.user_id,
                "version": 1,
                "status": "active",
            }

            from src.services.supabase_service import get_supabase_service

            supabase_service = await get_supabase_service()
            client = supabase_service.service_client
            client.table("roadmaps").insert(roadmap_data).execute()

            indexer_input = {
                "roadmap_id": None,
                "project_id": session.project_id,
                "title": roadmap_data["title"],
                "improved_roadmap": roadmap,
                "source_refs": [],
                "owner_id": session.user_id,
            }

            from src.agents.indexer_agent import IndexerAgent

            indexer_agent = IndexerAgent()
            indexer_result = await indexer_agent.execute(indexer_input)
            if not indexer_result.get("success"):
                logger.warning(f"IndexerAgent failed: {indexer_result.get('error')}")

            await ConversationRepository.update_interview_session_state(
                db=db, session_id=session.session_id, new_state=InterviewState.COMPLETE
            )

            # Store learning outcomes in memory for future personalization
            if self.memory_service:
                try:
                    imports = self._get_imports()
                    MemoryType = imports["MemoryType"]
                    # Store the generated roadmap as a learning outcome
                    roadmap_summary = f"Generated roadmap for project with phases: {', '.join([phase.get('name', 'Unknown') for phase in roadmap.get('phases', [])])}"
                    await self.memory_service.store_memory(
                        owner_id=session.user_id,
                        memory_type=MemoryType.LEARNING_OUTCOME,
                        content=roadmap_summary,
                        metadata={
                            "roadmap_id": roadmap_id,
                            "project_id": session.project_id,
                            "technology_stack": roadmap.get("technology_stack", []),
                            "estimated_timeline": roadmap.get("timeline", "Unknown"),
                            "key_features": roadmap.get("key_features", []),
                            "risks_identified": roadmap.get("risks", []),
                        },
                    )

                    # Store user responses as preferences for future interviews
                    responses = await self._get_responses_for_roadmap(
                        db, session.session_id, ConversationRepository
                    )
                    if responses:
                        preferences_content = f"User project preferences: {', '.join([f'{k}: {v}' for k, v in responses.items() if v])}"
                        await self.memory_service.store_memory(
                            owner_id=session.user_id,
                            memory_type=MemoryType.USER_PREFERENCE,
                            content=preferences_content,
                            metadata={
                                "project_type": responses.get("project_type", ""),
                                "technology_choices": responses.get("preferred_technologies", ""),
                                "timeline_preference": responses.get("timeline", ""),
                                "budget_constraints": responses.get("budget", ""),
                                "source": "architect_interview",
                            },
                        )
                except Exception as e:
                    logger.warning(f"Failed to store learning outcomes in memory: {e}")

            return {
                "agent": "architect",
                "status": "roadmap_generated",
                "session_id": session.session_id,
                "roadmap": roadmap,
                "roadmap_id": indexer_result.get("roadmap_id", roadmap_id),
                "indexer_result": indexer_result,
                "message": "Your project roadmap has been generated and indexed successfully!",
                "next_action": "interview_complete",
            }
        except Exception as e:
            logger.error(f"Failed to generate roadmap: {e}")
            return {
                "agent": "architect",
                "status": "error",
                "session_id": session.session_id,
                "error": f"Failed to generate roadmap: {str(e)}",
            }

    async def _handle_complete_state(self, db, session, ConversationRepository) -> Dict[str, Any]:
        """Handle the complete state - interview is finished."""
        return {
            "agent": "architect",
            "status": "interview_complete",
            "session_id": session.session_id,
            "message": "Your project interview is complete. You can now proceed with development!",
            "next_action": "finished",
        }

    async def _get_responses_for_roadmap(
        self, db, session_id: str, ConversationRepository
    ) -> Dict[str, str]:
        """Get all user responses formatted for roadmap generation."""
        conversations = await ConversationRepository.get_session_history(db, session_id)
        responses = {
            conv.question_key: conv.user_response for conv in conversations if conv.user_response
        }
        return responses

    async def _generate_roadmap(self, responses: Dict[str, str]) -> Dict[str, Any]:
        """Generate a comprehensive project roadmap from user responses."""
        roadmap_prompt = f"""
        Based on the following project information, create a detailed project roadmap:

        Project Information:
        {json.dumps(responses, indent=2)}

        Generate a comprehensive project roadmap in JSON format with the following structure:
        {{
            "overview": "Brief project overview",
            "technology_stack": ["tech1", "tech2"],
            "phases": [
                {{
                    "name": "Phase Name",
                    "duration": "X weeks",
                    "description": "Phase description",
                    "deliverables": ["deliverable1", "deliverable2"],
                    "key_features": ["feature1", "feature2"]
                }}
            ],
            "key_features": ["feature1", "feature2"],
            "risks": ["risk1", "risk2"],
            "timeline": "Overall timeline estimate"
        }}
        """
        try:
            llm = await self._get_llm()
            imports = self._get_imports()
            GenerateRequest = imports["GenerateRequest"]
            req = GenerateRequest(prompt=roadmap_prompt)
            resp = await llm.generate(req, user_id="architect-agent")
            return json.loads(resp.content.strip())
        except Exception as e:
            logger.error(f"Failed to generate roadmap: {e}")
            return {
                "overview": f"Project roadmap for {responses.get('project_name', 'Unnamed Project')}",
                "technology_stack": ["TBD"],
                "phases": [
                    {
                        "name": "Planning",
                        "duration": "1-2 weeks",
                        "description": "Requirements gathering and planning",
                        "deliverables": ["Requirements document", "Technical specification"],
                    },
                    {
                        "name": "Development",
                        "duration": "TBD",
                        "description": "Core development phase",
                        "deliverables": ["Working application"],
                    },
                ],
                "key_features": responses.get("key_features", "").split(",")
                if responses.get("key_features")
                else [],
                "risks": ["Technical challenges", "Scope changes"],
                "timeline": responses.get("timeline", "TBD"),
            }

    # =====================================================================================
    # Planning and Delegation Flow Methods
    # =====================================================================================

    async def _execute_planning_flow(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Plan and delegate tasks for the given user request."""
        user_goal = (
            task_input.get("goal")
            or task_input.get("feature")
            or task_input.get("request")
            or task_input.get("description")
            or "Build requested feature"
        )
        project_id = task_input.get("project_id")
        if not isinstance(project_id, int):
            return {
                "agent": "architect",
                "success": False,
                "error": "'project_id' (int) is required to create tasks",
            }

        if task_input.get("phase_completion_check", False):
            return await self._handle_phase_completion_check(project_id)
        if approval_response := task_input.get("approval_response"):
            return await self._handle_approval_response(approval_response, project_id)

        imports = self._get_imports()
        GenerateRequest = imports["GenerateRequest"]
        llm = await self._get_llm()
        prompt = self._planning_prompt(str(user_goal))
        req = GenerateRequest(prompt=prompt)
        resp = await llm.generate(req, user_id="architect-agent")

        plan_data = self._extract_json(resp.content)
        plan = self._normalize_plan(plan_data)

        TaskRepository = imports["TaskRepository"]
        get_async_db = imports["get_async_db"]
        created_task_ids: List[int] = []
        async with get_async_db() as db:
            from src.models.project import Project

            project = db.execute(select(Project).where(Project.id == project_id)).scalars().first()
            if not project:
                return {
                    "agent": "architect",
                    "status": "error",
                    "error": f"Project {project_id} not found",
                }

            for step in plan:
                input_data = {
                    "feature": step["task_description"],
                    "source": "architect_plan",
                    "goal": user_goal,
                    "integration_points": step.get("integration_points", []),
                    "db_connection_url": project.db_connection_url,
                    "db_schema_name": project.db_schema_name,
                    "project_id": project_id,
                }
                task = await TaskRepository.create_task(
                    db, project_id=project_id, agent_role=step["agent_role"], input_data=input_data
                )
                created_task_ids.append(task.id)

        return {
            "agent": "architect",
            "success": True,
            "goal": user_goal,
            "plan": plan,
            "created_task_ids": created_task_ids,
            "llm": {"model": resp.model, "provider": resp.provider.value},
        }

    @staticmethod
    def _planning_prompt(user_goal: str) -> str:
        allowed_roles = ["backend", "frontend", "shell"]
        return (
            "You are the Architect agent (project manager). Break down the user's goal into a step-by-step, "
            "actionable plan. Each step must be assigned to one of the specialist agent roles: "
            f"{allowed_roles}.\n\n"
            "For each task, you must also provide integration_points - a list of strings describing which "
            "existing files need to be modified to use the new feature. This ensures the new code is properly "
            "integrated into the existing application.\n\n"
            "Return ONLY JSON using this schema (no extra text, no markdown, no code fences):\n"
            "{\n"
            '  "plan": [\n'
            "    {\n"
            '      "agent_role": "backend|frontend|shell",\n'
            '      "task_description": "string",\n'
            '      "integration_points": [\n'
            '        "string describing which existing files to modify"\n'
            "      ]\n"
            "    }\n"
            "  ]\n"
            "}\n\n"
            f"User goal: {user_goal}\n"
        )

    @staticmethod
    def _extract_json(text: str) -> Dict[str, Any]:
        match = re.search(r"```(?:json)?\n(.*?)```", text, flags=re.DOTALL | re.IGNORECASE)
        candidate = match.group(1).strip() if match else text.strip()
        try:
            return json.loads(candidate)
        except Exception:
            pass
        start = candidate.find("{")
        end = candidate.rfind("}")
        if start != -1 and end != -1 and end > start:
            try:
                return json.loads(candidate[start : end + 1])
            except Exception:
                pass
        return {"plan": []}

    @staticmethod
    def _normalize_plan(plan_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        normalized: List[Dict[str, Any]] = []
        raw_list = plan_data.get("plan")
        if not isinstance(raw_list, list):
            return normalized
        allowed = {"backend", "frontend", "shell"}
        for step in raw_list:
            if not isinstance(step, dict):
                continue
            role = str(step.get("agent_role", "")).strip().lower()
            desc = str(step.get("task_description", "")).strip()
            integration_points = step.get("integration_points", [])
            if not isinstance(integration_points, list):
                integration_points = []
            else:
                integration_points = [str(point).strip() for point in integration_points if point]
            if role in allowed and desc:
                normalized.append(
                    {
                        "agent_role": role,
                        "task_description": desc,
                        "integration_points": integration_points,
                    }
                )
        return normalized

    async def _handle_phase_completion_check(self, project_id: int) -> Dict[str, Any]:
        """Check if any phases are complete and require user approval."""
        imports = self._get_imports()
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            from src.models.project import Project
            from src.models.roadmap import RoadmapItem

            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                return {
                    "agent": "architect",
                    "success": False,
                    "error": f"Project {project_id} not found",
                }

            if project.status == "pending_user_review":
                return {
                    "agent": "architect",
                    "status": "waiting_for_approval",
                    "message": "Project is waiting for user approval before proceeding",
                }

            completed_phases = (
                db.query(RoadmapItem)
                .filter(
                    RoadmapItem.project_id == project_id,
                    RoadmapItem.level == 1,
                    RoadmapItem.status == "completed",
                )
                .all()
            )

            for phase in completed_phases:
                if await self._has_pending_approval(db, project_id, phase.id):
                    continue
                approval_result = await self._create_phase_approval_request(db, project_id, phase)
                if approval_result["success"]:
                    project.status = "pending_user_review"
                    await db.commit()
                    return {
                        "agent": "architect",
                        "status": "approval_requested",
                        "phase_id": phase.id,
                        "phase_title": phase.title,
                        "approval_id": approval_result["approval_id"],
                        "message": f"Phase '{phase.title}' completed. User approval requested.",
                    }
            return {
                "agent": "architect",
                "status": "no_action_needed",
                "message": "No completed phases require approval at this time",
            }

    async def _handle_approval_response(
        self, approval_response: Dict[str, Any], project_id: int
    ) -> Dict[str, Any]:
        """Handle user approval/rejection response."""
        decision = approval_response.get("decision")
        comments = approval_response.get("comments", "")
        user_id = str(approval_response.get("user_id", "")).strip()

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id is required in the approval response",
            )

        imports = self._get_imports()
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            from src.models.project import Project

            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                return {
                    "agent": "architect",
                    "success": False,
                    "error": f"Project {project_id} not found",
                }

            if decision == "approved":
                project.status = "in_progress"
                await db.commit()
                if comments:
                    await self._process_approval_feedback(db, project_id, comments, user_id)
                return {
                    "agent": "architect",
                    "status": "approved",
                    "message": "Phase approved. Continuing with next phase.",
                    "next_action": "continue_build",
                }
            elif decision == "rejected":
                project.status = "in_progress"
                await db.commit()
                if comments:
                    feedback_tasks = await self._create_feedback_tasks(
                        db, project_id, comments, user_id
                    )
                    return {
                        "agent": "architect",
                        "status": "rejected_with_feedback",
                        "message": f"Phase rejected. Created {len(feedback_tasks)} new tasks based on feedback.",
                        "created_task_ids": feedback_tasks,
                        "next_action": "process_feedback",
                    }
                else:
                    return {
                        "agent": "architect",
                        "status": "rejected",
                        "message": "Phase rejected. Ready for retry or modification.",
                        "next_action": "await_instructions",
                    }
            else:
                return {
                    "agent": "architect",
                    "success": False,
                    "error": f"Unknown decision: {decision}",
                }

    async def _has_pending_approval(self, db, project_id: int, phase_id: int) -> bool:
        """Check if there's already a pending approval for this phase."""
        return False

    async def _create_phase_approval_request(self, db, project_id: int, phase) -> Dict[str, Any]:
        """Create an approval request for phase completion."""
        try:
            from src.models.roadmap import RoadmapItem

            total_tasks = (
                db.query(RoadmapItem)
                .filter(RoadmapItem.project_id == project_id, RoadmapItem.parent_id == phase.id)
                .count()
            )
            completed_tasks = (
                db.query(RoadmapItem)
                .filter(
                    RoadmapItem.project_id == project_id,
                    RoadmapItem.parent_id == phase.id,
                    RoadmapItem.status == "completed",
                )
                .count()
            )
            user_id = str(phase.owner_id)  # Use the actual owner_id from the phase/roadmap
            approval_data = {
                "phase_id": str(phase.id),
                "user_id": user_id,
                "phase_data": {
                    "title": phase.title,
                    "roadmap_id": str(project_id),
                    "completed_tasks": completed_tasks,
                    "total_tasks": total_tasks,
                    "files_created": [],
                    "files_modified": [],
                    "services_affected": [],
                },
            }

            # TODO: Refactor this to use an injected ApprovalManager service instead of an HTTP call.
            # This is a temporary fix to remove a major anti-pattern.
            logger.warning(
                "Skipping phase approval API call due to architectural issue. This needs to be refactored."
            )
            # The original implementation made an insecure and inefficient HTTP call to its own API.
            # The correct implementation should involve an injected service that handles approvals.
            # For now, we will simulate a successful request to allow the flow to continue.
            simulated_approval_id = f"approval_{uuid.uuid4()}"
            return {"success": True, "approval_id": simulated_approval_id}

        except Exception as e:
            logger.error(f"Failed to create phase approval: {e}")
            return {"success": False, "error": str(e)}

    async def _process_approval_feedback(
        self, db, project_id: int, comments: str, user_id: str
    ) -> None:
        """Process user feedback from approval comments."""
        logger.info(f"Processing approval feedback for project {project_id}: {comments}")

    async def _create_feedback_tasks(
        self, db, project_id: int, comments: str, user_id: str
    ) -> List[int]:
        """Create new tasks based on user feedback."""
        feedback_prompt = f"""
        User provided feedback on a completed phase: "{comments}"

        Based on this feedback, suggest 1-3 specific tasks that should be created to address the user's concerns.
        Return as JSON:
        {{
            "tasks": [
                {{
                    "description": "Task description",
                    "agent_role": "backend|frontend|shell",
                    "priority": "high|medium|low"
                }}
            ]
        }}
        """
        try:
            imports = self._get_imports()
            GenerateRequest = imports["GenerateRequest"]
            TaskRepository = imports["TaskRepository"]
            llm = await self._get_llm()
            req = GenerateRequest(prompt=feedback_prompt)
            resp = await llm.generate(req, user_id="architect-agent")
            feedback_data = self._extract_json(resp.content)
            tasks = feedback_data.get("tasks", [])
            created_task_ids = []
            for task in tasks:
                input_data = {
                    "feature": task["description"],
                    "source": "user_feedback",
                    "priority": task.get("priority", "medium"),
                    "user_feedback": comments,
                    "project_id": project_id,
                }
                new_task = await TaskRepository.create_task(
                    db, project_id=project_id, agent_role=task["agent_role"], input_data=input_data
                )
                created_task_ids.append(new_task.id)
            return created_task_ids
        except Exception as e:
            logger.error(f"Failed to create feedback tasks: {e}")
            return []

    async def handle_export_request(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle project export request by creating a structured plan."""
        try:
            logger.info(f"ArchitectAgent handling export request: {export_task['export_id']}")
            export_plan = await self._create_export_plan(export_task)
            task_ids = []
            if export_task.get("include_database", True):
                db_task = await self._create_database_export_task(export_task)
                task_ids.append(db_task["id"])
            if export_task.get("include_files", True):
                fs_task = await self._create_filesystem_export_task(export_task)
                task_ids.append(fs_task["id"])
            package_task = await self._create_packaging_task(export_task, task_ids)
            task_ids.append(package_task["id"])
            cleanup_task = await self._create_cleanup_task(export_task)
            task_ids.append(cleanup_task["id"])
            logger.info(
                f"Export plan created with {len(task_ids)} tasks for export {export_task['export_id']}"
            )
            return {
                "success": True,
                "export_id": export_task["export_id"],
                "plan": export_plan,
                "task_ids": task_ids,
                "estimated_duration": "5-10 minutes",
                "agent": "architect",
                "status": "export_plan_created",
            }
        except Exception as e:
            logger.error(f"Export plan creation failed for {export_task.get('export_id')}: {e}")
            return {
                "success": False,
                "error": str(e),
                "export_id": export_task.get("export_id"),
                "agent": "architect",
                "status": "export_plan_failed",
            }

    async def _create_export_plan(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create a structured export plan."""
        steps = []
        if export_task.get("include_database", True):
            steps.append(
                {
                    "step": "database_export",
                    "description": f"Export database data for project {export_task['project_name']}",
                    "agent_role": "shell",
                    "estimated_duration": "2-3 minutes",
                }
            )
        if export_task.get("include_files", True):
            steps.append(
                {
                    "step": "filesystem_export",
                    "description": f"Copy project files for {export_task['project_name']}",
                    "agent_role": "shell",
                    "estimated_duration": "1-2 minutes",
                }
            )
        steps.extend(
            [
                {
                    "step": "package_export",
                    "description": f"Create {export_task.get('export_format', 'zip')} archive",
                    "agent_role": "shell",
                    "estimated_duration": "1-2 minutes",
                },
                {
                    "step": "cleanup_export",
                    "description": "Clean up temporary files",
                    "agent_role": "shell",
                    "estimated_duration": "30 seconds",
                },
            ]
        )
        return {
            "export_type": "project_export",
            "project_name": export_task["project_name"],
            "export_format": export_task.get("export_format", "zip"),
            "include_database": export_task.get("include_database", True),
            "include_files": export_task.get("include_files", True),
            "steps": steps,
            "total_steps": len(steps),
            "created_at": datetime.utcnow().isoformat(),
        }

    async def _create_database_export_task(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create database export task for ShellAgent."""
        task_data = {
            "project_id": export_task["project_id"],
            "agent_role": "shell",
            "title": f"Export Database for {export_task['project_name']}",
            "description": "Export project-specific database data using pg_dump",
            "input_data": {
                "command_type": "database_export",
                "export_id": export_task["export_id"],
                "project_id": export_task["project_id"],
                "user_id": export_task["user_id"],
                "project_name": export_task["project_name"],
            },
            "priority": "high",
            "timeout_minutes": 10,
        }
        imports = self._get_imports()
        TaskRepository = imports["TaskRepository"]
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            task_repo = TaskRepository(db)
            task = await task_repo.create_task(task_data)
            return {"id": task.id, "title": task.title}

    async def _create_filesystem_export_task(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create filesystem export task for ShellAgent."""
        task_data = {
            "project_id": export_task["project_id"],
            "agent_role": "shell",
            "title": f"Export Files for {export_task['project_name']}",
            "description": "Copy project files to export staging area",
            "input_data": {
                "command_type": "filesystem_export",
                "export_id": export_task["export_id"],
                "project_id": export_task["project_id"],
                "user_id": export_task["user_id"],
                "project_name": export_task["project_name"],
            },
            "priority": "high",
            "timeout_minutes": 5,
        }
        imports = self._get_imports()
        TaskRepository = imports["TaskRepository"]
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            task_repo = TaskRepository(db)
            task = await task_repo.create_task(task_data)
            return {"id": task.id, "title": task.title}

    async def _create_packaging_task(
        self, export_task: Dict[str, Any], dependency_task_ids: List[str]
    ) -> Dict[str, Any]:
        """Create packaging task for ShellAgent."""
        task_data = {
            "project_id": export_task["project_id"],
            "agent_role": "shell",
            "title": f"Package Export for {export_task['project_name']}",
            "description": "Create compressed archive of exported project",
            "input_data": {
                "command_type": "package_export",
                "export_id": export_task["export_id"],
                "project_id": export_task["project_id"],
                "user_id": export_task["user_id"],
                "project_name": export_task["project_name"],
                "export_format": export_task.get("export_format", "zip"),
            },
            "dependencies": dependency_task_ids,
            "priority": "high",
            "timeout_minutes": 5,
        }
        imports = self._get_imports()
        TaskRepository = imports["TaskRepository"]
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            task_repo = TaskRepository(db)
            task = await task_repo.create_task(task_data)
            return {"id": task.id, "title": task.title}

    async def _create_cleanup_task(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create cleanup task for ShellAgent."""
        task_data = {
            "project_id": export_task["project_id"],
            "agent_role": "shell",
            "title": f"Cleanup Export for {export_task['project_name']}",
            "description": "Clean up temporary export files",
            "input_data": {
                "command_type": "cleanup_export",
                "export_id": export_task["export_id"],
                "project_id": export_task["project_id"],
                "user_id": export_task["user_id"],
                "project_name": export_task["project_name"],
            },
            "priority": "low",
            "timeout_minutes": 2,
        }
        imports = self._get_imports()
        TaskRepository = imports["TaskRepository"]
        get_async_db = imports["get_async_db"]
        async with get_async_db() as db:
            task_repo = TaskRepository(db)
            task = await task_repo.create_task(task_data)
            return {"id": task.id, "title": task.title}

    async def _validate_agent_specific_prerequisites(
        self, task_input: Dict[str, Any]
    ) -> "ValidationResult":
        """Validate ArchitectAgent-specific prerequisites."""
        from src.agents.base_agent import ValidationResult

        project_id = task_input.get("project_id")
        if not isinstance(project_id, int):
            return ValidationResult.failure("'project_id' (int) is required to create tasks")
        return ValidationResult.success("Prerequisites validated")

    async def _validate_agent_specific_completion(
        self, task_input: Dict[str, Any], result: Dict[str, Any]
    ) -> "ValidationResult":
        """Validate ArchitectAgent-specific completion."""
        from src.agents.base_agent import ValidationResult

        if result.get("status") == "success":
            if "plan" not in result:
                return ValidationResult.failure("Architect should return a plan")
            if "created_task_ids" not in result:
                return ValidationResult.failure("Architect should return created task IDs")
        return ValidationResult.success("Completion validated")
