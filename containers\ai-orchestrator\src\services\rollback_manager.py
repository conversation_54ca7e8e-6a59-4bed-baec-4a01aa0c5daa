"""
Rollback Manager for AI Coding Agent

Provides comprehensive rollback capabilities for failed provisioning attempts
with checkpoint management and atomic operations.
"""

import asyncio
import logging
import shutil
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from enum import Enum

# Docker availability check
try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    docker = None

logger = logging.getLogger(__name__)


class RollbackStage(Enum):
    """Stages of provisioning that can be rolled back."""
    DIRECTORY_CREATION = "directory_creation"
    TEMPLATE_COPYING = "template_copying"
    PLACEHOLDER_REPLACEMENT = "placeholder_replacement"
    DATABASE_RECORD = "database_record"
    USER_ASSOCIATION = "user_association"
    DOCKER_COMPOSE_UP = "docker_compose_up"
    CONTAINER_HEALTH = "container_health"
    HEALTH_ENDPOINT = "health_endpoint"


@dataclass
class RollbackCheckpoint:
    """Checkpoint for rollback operations."""
    checkpoint_id: str
    project_name: str
    user_id: str
    stage: RollbackStage
    timestamp: str
    data: Dict[str, Any]
    rollback_actions: List[str]
    status: str  # 'active', 'rolled_back', 'committed'


class RollbackManager:
    """Manages rollback operations for provisioning failures."""

    def __init__(self):
        self.checkpoints: Dict[str, RollbackCheckpoint] = {}
        self.rollback_lock = asyncio.Lock()
        self.checkpoints_dir = Path(__file__).parent.parent.parent / "data" / "checkpoints"
        self.checkpoints_dir.mkdir(parents=True, exist_ok=True)

    async def create_checkpoint(
        self,
        project_name: str,
        user_id: str,
        stage: RollbackStage,
        data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a rollback checkpoint."""
        checkpoint_id = f"{user_id}_{project_name}_{stage.value}_{int(datetime.utcnow().timestamp())}"

        rollback_actions = self._get_rollback_actions_for_stage(stage, data or {})

        checkpoint = RollbackCheckpoint(
            checkpoint_id=checkpoint_id,
            project_name=project_name,
            user_id=user_id,
            stage=stage,
            timestamp=datetime.utcnow().isoformat(),
            data=data or {},
            rollback_actions=rollback_actions,
            status="active"
        )

        self.checkpoints[checkpoint_id] = checkpoint
        await self._save_checkpoint(checkpoint)

        logger.info(f"Created rollback checkpoint: {checkpoint_id} at stage {stage.value}")
        return checkpoint_id

    def _get_rollback_actions_for_stage(self, stage: RollbackStage, data: Dict[str, Any]) -> List[str]:
        """Get rollback actions required for a specific stage."""
        actions = []

        if stage == RollbackStage.DIRECTORY_CREATION:
            actions = [
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage == RollbackStage.TEMPLATE_COPYING:
            actions = [
                f"remove_template_files:{data.get('project_path', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage == RollbackStage.PLACEHOLDER_REPLACEMENT:
            actions = [
                f"remove_generated_files:{data.get('project_path', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage == RollbackStage.DATABASE_RECORD:
            actions = [
                f"delete_project_record:{data.get('project_id', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage == RollbackStage.USER_ASSOCIATION:
            actions = [
                f"remove_user_association:{data.get('project_id', '')}:{data.get('user_id', '')}",
                f"delete_project_record:{data.get('project_id', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage == RollbackStage.DOCKER_COMPOSE_UP:
            actions = [
                f"stop_containers:{data.get('project_name', '')}",
                f"remove_containers:{data.get('project_name', '')}",
                f"remove_networks:{data.get('project_name', '')}",
                f"remove_user_association:{data.get('project_id', '')}:{data.get('user_id', '')}",
                f"delete_project_record:{data.get('project_id', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        elif stage in [RollbackStage.CONTAINER_HEALTH, RollbackStage.HEALTH_ENDPOINT]:
            actions = [
                f"stop_containers:{data.get('project_name', '')}",
                f"remove_containers:{data.get('project_name', '')}",
                f"remove_networks:{data.get('project_name', '')}",
                f"remove_user_association:{data.get('project_id', '')}:{data.get('user_id', '')}",
                f"delete_project_record:{data.get('project_id', '')}",
                f"remove_directory:{data.get('project_path', '')}"
            ]

        return actions

    async def rollback_to_checkpoint(self, checkpoint_id: str) -> bool:
        """Execute rollback to a specific checkpoint."""
        async with self.rollback_lock:
            if checkpoint_id not in self.checkpoints:
                logger.error(f"Checkpoint not found: {checkpoint_id}")
                return False

            checkpoint = self.checkpoints[checkpoint_id]
            if checkpoint.status != "active":
                logger.warning(f"Checkpoint {checkpoint_id} is not active (status: {checkpoint.status})")
                return False

            logger.info(f"Starting rollback to checkpoint: {checkpoint_id}")

            try:
                # Execute rollback actions in reverse order
                for action in reversed(checkpoint.rollback_actions):
                    await self._execute_rollback_action(action, checkpoint)

                # Mark checkpoint as rolled back
                checkpoint.status = "rolled_back"
                await self._save_checkpoint(checkpoint)

                logger.info(f"Successfully rolled back to checkpoint: {checkpoint_id}")
                return True

            except Exception as e:
                logger.error(f"Rollback failed for checkpoint {checkpoint_id}: {e}")
                return False

    async def _execute_rollback_action(self, action: str, checkpoint: RollbackCheckpoint):
        """Execute a specific rollback action."""
        action_type, action_data = action.split(":", 1) if ":" in action else (action, "")

        try:
            if action_type == "remove_directory":
                await self._rollback_remove_directory(action_data)

            elif action_type == "remove_template_files":
                await self._rollback_remove_template_files(action_data)

            elif action_type == "remove_generated_files":
                await self._rollback_remove_generated_files(action_data)

            elif action_type == "delete_project_record":
                await self._rollback_delete_project_record(action_data)

            elif action_type == "remove_user_association":
                await self._rollback_remove_user_association(action_data)

            elif action_type == "stop_containers":
                await self._rollback_stop_containers(action_data)

            elif action_type == "remove_containers":
                await self._rollback_remove_containers(action_data)

            elif action_type == "remove_networks":
                await self._rollback_remove_networks(action_data)

            else:
                logger.warning(f"Unknown rollback action: {action_type}")

            logger.debug(f"Executed rollback action: {action}")

        except Exception as e:
            logger.error(f"Failed to execute rollback action {action}: {e}")
            raise

    async def _rollback_remove_directory(self, directory_path: str):
        """Remove project directory."""
        if directory_path and Path(directory_path).exists():
            shutil.rmtree(directory_path)
            logger.info(f"Removed directory: {directory_path}")

    async def _rollback_remove_template_files(self, directory_path: str):
        """Remove template files from directory."""
        if directory_path and Path(directory_path).exists():
            # Remove specific template-generated files
            project_path = Path(directory_path)
            template_files = [
                "Dockerfile", "docker-compose.yml", ".env.example",
                "requirements.txt", "init-db.sql", "README.md"
            ]

            for file_name in template_files:
                file_path = project_path / file_name
                if file_path.exists():
                    file_path.unlink()

            # Remove src directory
            src_dir = project_path / "src"
            if src_dir.exists():
                shutil.rmtree(src_dir)

    async def _rollback_remove_generated_files(self, directory_path: str):
        """Remove all generated files."""
        await self._rollback_remove_template_files(directory_path)

    async def _rollback_delete_project_record(self, project_id: str):
        """Delete project record from database."""
        if not project_id:
            return

        try:
            # This would integrate with the database session
            from src.database.session import SessionLocal
            from src.models import Project

            db = SessionLocal()
            try:
                project = db.query(Project).filter(Project.id == project_id).first()
                if project:
                    db.delete(project)
                    db.commit()
                    logger.info(f"Deleted project record: {project_id}")
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to delete project record {project_id}: {e}")

    async def _rollback_remove_user_association(self, association_data: str):
        """Remove user-project association."""
        if ":" not in association_data:
            return

        project_id, user_id = association_data.split(":", 1)

        try:
            from src.database.session import SessionLocal
            from src.models import user_project_association

            db = SessionLocal()
            try:
                db.execute(
                    user_project_association.delete().where(
                        user_project_association.c.project_id == project_id,
                        user_project_association.c.user_id == user_id
                    )
                )
                db.commit()
                logger.info(f"Removed user association: {user_id} -> {project_id}")
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to remove user association: {e}")

    async def _rollback_stop_containers(self, project_name: str):
        """Stop Docker containers for project."""
        if not project_name or not DOCKER_AVAILABLE:
            logger.warning("Docker not available or no project name provided")
            return

        try:
            client = docker.from_env()

            containers = await asyncio.to_thread(
                client.containers.list,
                filters={"name": f"{project_name}-"}
            )

            for container in containers:
                await asyncio.to_thread(container.stop, timeout=10)
                logger.info(f"Stopped container: {container.name}")

        except Exception as e:
            logger.error(f"Failed to stop containers for {project_name}: {e}")

    async def _rollback_remove_containers(self, project_name: str):
        """Remove Docker containers for project."""
        if not project_name or not DOCKER_AVAILABLE:
            logger.warning("Docker not available or no project name provided")
            return

        try:
            client = docker.from_env()

            containers = await asyncio.to_thread(
                client.containers.list,
                all=True,
                filters={"name": f"{project_name}-"}
            )

            for container in containers:
                await asyncio.to_thread(container.remove, force=True)
                logger.info(f"Removed container: {container.name}")

        except Exception as e:
            logger.error(f"Failed to remove containers for {project_name}: {e}")

    async def _rollback_remove_networks(self, project_name: str):
        """Remove Docker networks for project."""
        if not project_name or not DOCKER_AVAILABLE:
            logger.warning("Docker not available or no project name provided")
            return

        try:
            client = docker.from_env()

            networks = await asyncio.to_thread(
                client.networks.list,
                filters={"name": f"{project_name}-network"}
            )

            for network in networks:
                await asyncio.to_thread(network.remove)
                logger.info(f"Removed network: {network.name}")

        except Exception as e:
            logger.error(f"Failed to remove networks for {project_name}: {e}")

    async def _save_checkpoint(self, checkpoint: RollbackCheckpoint):
        """Save checkpoint to disk."""
        checkpoint_file = self.checkpoints_dir / f"{checkpoint.checkpoint_id}.json"

        with open(checkpoint_file, 'w') as f:
            json.dump(asdict(checkpoint), f, indent=2)

    async def cleanup_old_checkpoints(self, max_age_hours: int = 24) -> int:
        """Clean up old checkpoints."""
        cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
        cleaned_count = 0

        for checkpoint_id, checkpoint in list(self.checkpoints.items()):
            checkpoint_time = datetime.fromisoformat(checkpoint.timestamp).timestamp()

            if checkpoint_time < cutoff_time and checkpoint.status in ["rolled_back", "committed"]:
                # Remove from memory
                del self.checkpoints[checkpoint_id]

                # Remove from disk
                checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
                if checkpoint_file.exists():
                    checkpoint_file.unlink()

                cleaned_count += 1
                logger.info(f"Cleaned up old checkpoint: {checkpoint_id}")

        return cleaned_count

    async def commit_checkpoint(self, checkpoint_id: str) -> bool:
        """Mark a checkpoint as committed (successful completion)."""
        if checkpoint_id in self.checkpoints:
            self.checkpoints[checkpoint_id].status = "committed"
            await self._save_checkpoint(self.checkpoints[checkpoint_id])
            logger.info(f"Committed checkpoint: {checkpoint_id}")
            return True
        return False


# Global rollback manager instance
rollback_manager = RollbackManager()


def get_rollback_manager() -> RollbackManager:
    """Get the global rollback manager instance."""
    return rollback_manager
