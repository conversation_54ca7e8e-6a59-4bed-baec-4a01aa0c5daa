# The correct content for src/models/__init__.py

# Only import database components by default to avoid model conflicts
from .database import Base, get_db
from .association import user_project_association

# Lazy import models to avoid conflicts during database testing
def __getattr__(name):
    if name == "UserProfile":
        from .user import UserProfile
        return UserProfile
    elif name == "User":
        # Backward compatibility - redirect to UserProfile
        from .user import UserProfile
        return UserProfile
    elif name == "Project":
        from .project import Project
        return Project

    elif name == "Task":
        from .task import Task
        return Task
    elif name == "AgentState":
        from .agent_state import AgentState
        return AgentState
    elif name == "LLMProvider":
        from .llm_config import LLMProvider
        return LLMProvider
    elif name == "LLMModel":
        from .llm_config import LLMModel
        return LLMModel
    elif name == "AgentModelAssignment":
        from .llm_config import AgentModelAssignment
        return AgentModelAssignment
    elif name == "ConversationHistory":
        from .conversation import ConversationHistory
        return ConversationHistory
    elif name == "InterviewSession":
        from .conversation import InterviewSession
        return InterviewSession
    elif name == "InterviewState":
        from .conversation import InterviewState
        return InterviewState
    elif name == "RoadmapItem":
        from .roadmap import RoadmapItem
        return RoadmapItem
    elif name == "Roadmap":
        from .roadmap import Roadmap
        return Roadmap
    elif name == "RoadmapSummary":
        from .roadmap import RoadmapSummary
        return RoadmapSummary
    elif name == "RoadmapSourceReference":
        from .roadmap import RoadmapSourceReference
        return RoadmapSourceReference
    elif name == "Workspace":
        from .workspace import Workspace
        return Workspace
    elif name == "IngestionError":
        from .ingestion_errors import IngestionError
        return IngestionError
    elif name == "user_project_association":
        from .association import user_project_association
        return user_project_association
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

# Remove __all__ since we're using lazy loading
