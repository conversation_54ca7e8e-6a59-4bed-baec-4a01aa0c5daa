from fastapi import APIRouter
from src.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Import all routers
from .routers import router as base_router
from .llm_router import router as llm_router
from .role_management import router as role_router
from .redis_router import router as redis_router
from .websocket_router import router as websocket_router
from .user_router import router as user_router
from .workspace_router import router as workspace_router
from .health_router import health_router
from .memory_router import router as memory_router
from .security_router import router as security_router
from .roadmap_router import router as roadmap_router
from .deployment_router import router as deployment_router
from .github_auth_router import router as github_auth_router
from .project_router import router as project_router
from .supabase_router import router as supabase_router
from .dispatcher_router import dispatcher_router


# Create a master router
api_router = APIRouter()

# Include core and always-on routers
api_router.include_router(base_router)
api_router.include_router(llm_router)
api_router.include_router(role_router)
api_router.include_router(redis_router)
api_router.include_router(websocket_router, prefix="/ws")
api_router.include_router(user_router, prefix="/api/v2")
api_router.include_router(workspace_router)
api_router.include_router(health_router)
api_router.include_router(memory_router)
api_router.include_router(security_router)
api_router.include_router(roadmap_router)
api_router.include_router(deployment_router)
api_router.include_router(dispatcher_router, prefix="/dispatcher", tags=["Agent Dispatcher"])


# Conditionally include optional routers
if settings.ENABLE_GITHUB_AUTH:
    api_router.include_router(github_auth_router, prefix="/auth/github")
    logger.info("GitHub authentication router included in api_router.")

if settings.ENABLE_PROJECT_MANAGEMENT:
    api_router.include_router(project_router, prefix="/api/projects")
    logger.info("Project management router included in api_router.")

if settings.USE_SUPABASE:
    api_router.include_router(supabase_router)
    logger.info("Supabase router included in api_router.")

# Conditionally include feature-guarded routers
try:
    from src.services.langgraph_orchestrator import (
        LANGGRAPH_AVAILABLE,
        langgraph_orchestrator_router,
    )
    if settings.ENABLE_LANGGRAPH and LANGGRAPH_AVAILABLE:
        api_router.include_router(
            langgraph_orchestrator_router,
            prefix="/langgraph",
            tags=["LangGraph Orchestrator"],
        )
        logger.info("LangGraph orchestrator router included in api_router.")
    else:
        logger.warning("LangGraph feature disabled or dependencies not found. Router not included.")
except ImportError:
    logger.warning("Could not import langgraph_orchestrator. Router not included.")

try:
    from src.router.cuda_router import CUDA_AVAILABLE, cuda_router
    if settings.ENABLE_CUDA and CUDA_AVAILABLE:
        api_router.include_router(
            cuda_router,
            prefix="/cuda",
            tags=["CUDA Accelerated Service"],
        )
        logger.info("CUDA accelerated router included in api_router.")
    else:
        logger.warning("CUDA feature disabled or dependencies not found. Router not included.")
except ImportError:
    logger.warning("Could not import cuda_router. Router not included.")
