FROM haproxy:2.8-alpine

# Switch to root to install packages (HAProxy Alpine defaults to non-root)
USER root

# Install curl for health checks (Context7 recommended approach)
RUN apk add --no-cache curl

# Copy HAProxy configuration
COPY haproxy.cfg /usr/local/etc/haproxy/haproxy.cfg

# Health check using curl (Context7 best practice)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:2377/health || exit 1

# Expose Docker API port and health check port
EXPOSE 2375 2377